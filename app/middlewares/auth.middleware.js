require('dotenv').config();
const { DateTime } = require('luxon');

const jwt = require('jsonwebtoken');
const userService = require('../services/auth.service');
const accountService = require('../services/account.service');
const projectServices = require('../services/project.service');
const memberService = require('../services/member.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const { toObjectId } = require('../utils/common.utils');
const HTTP_STATUS = require('../utils/status-codes');

exports.assignToken = async (user, role, roleId, isMobile) => {
  const account = await accountService.findAccountById(user.account);
  let token = await jwt.sign(
    {
      id: user._id,
      role,
      account: user.account,
      syncTime: account?.syncUpTime || parseInt(process.env.DEFAULT_SYNCUP_TIME),
      roleId,
      isMobile, //Logged in from mobile device or not
    },
    process.env.JWT_SECRET,
    {
      algorithm: 'HS256',
    },
    { expiresIn: '24h' /* expires in 24 hours*/ }
  );
  return token;
};

exports.verifyToken = async (req, res, next) => {
  try {
    const token = req.headers.authorization;
    if (!token) {
      return res.status(400).json({
        status: false,
        message: constantUtils.SEND_AUTHENTICATION_TOKEN,
      });
    }
    const decode = await jwt.verify(token.split(' ')[1], process.env.JWT_SECRET);
    if (decode) {
      const user = await userService.getUserById(decode.id);

      if (user) {
        user.isMobile = decode.isMobile;
        req.userData = user;
      } else {
        return res.status(400).json({
          status: false,
          message: constantUtils.USER_CANNOT_ACCESS_USER_NOT_FOUND,
        });
      }
    }

    if (req.userData.role.isAssignAllProjects) {
      const projectIds = await projectServices.getAllProjects({
        account: req.userData.account,
        deletedAt: null,
      });

      if (projectIds.length > 0) {
        req.assignedProjectList = projectIds.map(data => data._id);
      }
    } else {
      const approverData = await memberService.getAllMember({
        account: req.userData.account,
        user: req.userData._id,
        isApprover: true,
        deletedAt: null,
      });
      if (approverData.length > 0) {
        let projectIds = approverData.map(data => data.project._id);
        req.assignedProjectList = projectIds;
      }
    }

    // Report role change
    if (!req.userData.role.isActive) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json({
        status: false,
        message: constantUtils.ROLE_INACTIVE,
      });
    }

    if (!req.userData.isActive) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json({
        status: false,
        message: constantUtils.ACCOUNT_INACTIVE,
      });
    }

    if (decode.isMobile && req.userData.role.accessType === global.constant.WEB) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json({
        status: false,
        message: constantUtils.WEB_ONLY,
      });
    }

    if (!decode.isMobile && req.userData.role.accessType === global.constant.MOBILE) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json({
        status: false,
        message: constantUtils.MOBILE_ONLY,
      });
    }
    // Report role change

    const headersTimezone = req.headers['x-timezone'];
    if (headersTimezone && this.isValidTimezone(headersTimezone)) {
      req.userTimezone = headersTimezone;
    } else {
      req.userTimezone = global.constant.DEFAULT_TIMEZONE;
    }

    return next();
  } catch (err) {
    return res.status(400).json({
      status: false,
      message: constantUtils.UNAUTHORIZED_ROLE_REQUESTED,
    });
  }
};

exports.deletedAt = async (req, res, next) => {
  const deletedAt = {
    deletedAt: new Date(),
    deletedBy: req.userData.id,
    isDeleted: true,
  };
  req.deletedAt = deletedAt;
  next();
};

exports.defaultCreatedDetails = async (req, res, next) => {
  req.body.account = req.userData.account;
  req.body.createdBy = req.userData.id;
  req.body.updatedBy = req.userData.id;
  next();
};

exports.updatedBy = async (req, res, next) => {
  req.body.updatedBy = req.userData.id;
  next();
};

/**
 * Check the account in request and add it to req.userData
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
exports.authAccount = async (req, res, next) => {
  // Check if the request method is GET or DELETE and if there is a query param for account
  if (['GET', 'DELETE'].includes(req.method) && req.query.account) {
    // Convert the account ID to an new mongoose.Types.ObjectId and set it to req.userData.account
    req.userData.account = toObjectId(req.query.account);
    delete req.query.account;
    delete req.query.admin;
  }

  // Check if the request method is POST, PATCH, or PUT and if there is a body param for account
  if (['POST', 'PATCH', 'PUT'].includes(req.method) && req.body.account) {
    // Convert the account ID to an new mongoose.Types.ObjectId and set it to req.userData.account
    req.userData.account = toObjectId(req.body.account);
    delete req.body.account;
    delete req.body.admin;
  }

  // Call the next middleware function
  next();
};

/**
 * Check project status
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @returns
 */
exports.checkProjectStatus = async (req, res, next) => {
  if (req.method === 'POST') {
    const getProject = await projectServices.getProjectById(req.body.project, req.userData.account);

    if (!getProject) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_PROJECT));
    }

    if (['completed', 'closed'].includes(getProject.status)) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(`Selected project is ${getProject.status}.`));
    }
    next();
  }

  next();
};

exports.checkWarehouseManager = async (req, res, next) => {
  if (
    req.userData.role?.title &&
    ![
      global.constant.WAREHOUSE_MANAGER_ROLE,
      global.constant.ADMIN_ROLE,
      global.constant.SUPER_ADMIN_ROLE,
    ].includes(req.userData.role.title)
  ) {
    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_PM_ORDER_REQUEST, []));
  }

  next();
};

exports.isValidTimezone = timezone => {
  try {
    return !!DateTime.local().setZone(timezone).isValid;
  } catch (e) {
    return false;
  }
};
